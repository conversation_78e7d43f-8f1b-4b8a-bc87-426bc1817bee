import { LightningElement, api } from 'lwc';
import { utils } from 'c/utils';

export default class CardGaranzieHeader extends LightningElement {
  _field;

  @api groups;

  @api
  set field(input) {
    if (input) {
      this._field = input;
    }
  }

  get field() {
    return this._field;
  }

  get checkbox() {
    const checkbox = utils.getFirstFieldInGroupsByType(this.groups, 'pxCheckbox');
    if (checkbox) checkbox.label = '';
    return checkbox;
  }

  get policyName() {
    const caption = utils.getFirstCaptionByControlFormat(this.groups, 'NomeGaranzia');
    return utils.captionToValue(caption)?.value || '';
  }

  get infoIcon() {
    const icon = utils.getFirstIconInGroupsByResource(this.groups, 'info');
    if (icon?.customAttributes) {
      icon.customAttributes.webResponsiveSize = '24L 24L 24L';
    }
    return icon;
  }

  get requiredLabel() {
    const caption = utils.getFirstCaptionByControlFormat(this.groups, 'TagObbligatoria');
    return utils.captionToValue(caption)?.value || '';
  }

  get lockIcon() {
    return utils.getFirstIconInGroupsByResource(this.groups, 'lock');
  }

  get isRequired() {
    return this.lockIcon?.visible || false;
  }

  get showLockIcon() {
    return this.isRequired;
  }

  get showCheckbox() {
    return !this.isRequired;
  }
}