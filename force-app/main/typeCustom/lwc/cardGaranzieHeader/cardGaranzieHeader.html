<template>
    <div class="CardGaranzieHeaderContainer">
      <input class="visibleMobile" type="hidden" />
  
      <span class="policyNameLabel">{policyName}</span>
  
      <c-field field={infoIcon}></c-field>
  
      <div class="RightSideContainer">

        <template if:true={isRequired}>
          <span class="requiredLabel">{requiredLabel}</span>
        </template>
        <template if:true={showCheckbox}>
          <c-field field={checkbox}></c-field>
        </template>
        <template if:true={showLockIcon}>
          <c-field field={lockIcon}></c-field>
        </template>
      </div>
    </div>
  </template>