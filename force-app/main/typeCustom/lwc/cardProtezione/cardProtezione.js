import { LightningElement, api } from 'lwc';
import { utils } from 'c/utils';

export default class CardProtezione extends LightningElement {
    _groups;

    @api
    get groups() {
        return this._groups;
    }
    set groups(value) {
        this._groups = value;
        this.initializeData();
    }

    get nomeGaranzia() {    
        const caption = utils.getFirstCaptionByControlFormat(this._groups, 'NomeGaranzia');
        return utils.captionToValue(caption)?.value || '';       
    }

    get descrizioneGaranzia() {
        const caption = utils.getFirstCaptionByControlFormat(this._groups, 'DescrizioneGaranzia');
        return utils.captionToValue(caption)?.value || '';  
    }

    get testoPrezzoGaranzia() {
        const caption = utils.getFirstCaptionByControlFormat(this._groups, 'TestoPrezzoGaranzia');
        return utils.captionToValue(caption)?.value || '';    

    }


    get premioRataScontato() {
        const result = utils.getFirstCurrencyByFieldId(this._groups, 'RataPremioLordoScontato');
        if (result) {
            return `${result.value}${result.symbol}`;
        }
    }

    get icon() {
        return utils.getFirstFieldInGroupsByType(this._groups, 'pxIcon');
       
    }

    get checkbox() {
        return utils.getFirstFieldInGroupsByType(this._groups, 'pxCheckbox');
    }

    initializeData() {
        if (this._field?.customAttributes) {
            this.assurancePackageHeader = this._field.customAttributes.assurancePackageHeader;
        }

        if (this.checkbox) {
            this.checked = this.checkbox.value === "true";
        }
    }
}
