.header {
  height: 84px;
  padding: 24px 140px;
  font-size: 24px;
  position: relative;
  display: flex;
  align-items: center;
  font-family: var(--font-family-default), sans-serif;
  color: white;
  justify-content: space-between;
}

.header > .back-btn {
  margin-right: 16px;
}

.header > .title {
  flex: 1;
}

.background-Property {
  background-color: #e94e10;
}

.background-Mobility {
  background-color: #0169b4;
}

.background-Secondary {
  background-color: var(--blue-primary);
}