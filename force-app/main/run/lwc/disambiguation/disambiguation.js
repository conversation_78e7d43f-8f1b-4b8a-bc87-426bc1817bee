import { api, LightningElement, track } from 'lwc';

export default class Disambiguation extends LightningElement {
  @api debug;

  get isV<PERSON><PERSON><PERSON>() {
    return this.request.productType === 'PUVEICOLO';
  }

  get showTarga() {
    return this.request.productType === 'PUVEICOLO' && !this.request.noTarga;
  }

  get addressDisplayer() {
    if (this.request.productType === 'PUVEICOLO') {
      return (
        this.request.referencesToUpdate['Ambito.Proprietario.Residenza.NomeStrada'] +
        ', ' +
        this.request.referencesToUpdate['Ambito.Proprietario.Residenza.NumeroCivico'] +
        ' - ' +
        this.request.referencesToUpdate['Ambito.Proprietario.Residenza.Cap'] +
        ' ' +
        this.request.referencesToUpdate['Ambito.Proprietario.Residenza.Comune'] +
        '(' +
        this.request.referencesToUpdate['Ambito.Proprietario.Residenza.Provincia'] +
        ')'
      );
    } else if (this.request.productType === 'PUCASA') {
      return (
        this.request.referencesToUpdate['Ambito.Bene.Casa.Indirizzo.NomeStrada'] +
        ', ' +
        this.request.referencesToUpdate['Ambito.Bene.Casa.Indirizzo.NumeroCivico'] +
        ' - ' +
        this.request.referencesToUpdate['Ambito.Bene.Casa.Indirizzo.Cap'] +
        ' ' +
        this.request.referencesToUpdate['Ambito.Bene.Casa.Indirizzo.Comune'] +
        '(' +
        this.request.referencesToUpdate['Ambito.Bene.Casa.Indirizzo.Provincia'] +
        ')'
      );
    }
  }

  get isCasa() {
    return this.request.productType === 'PUCASA';
  }

  get isSalute() {
    return this.request.productType === 'PUSALUTE';
  }

  get isInfortuni() {
    return this.request.productType === 'PUINFORTUNI';
  }

  get isMobilita() {
    return this.request.productType === 'PUMOBILITA';
  }

  get isViaggi() {
    return this.request.productType === 'PUVIAGGI';
  }

  get isPet() {
    return this.request.productType === 'PUPET';
  }

  get isFamiglia() {
    return this.request.productType === 'PUFAMIGLIA';
  }

  @track request = { productType: '', referencesToUpdate: {} };
  @track fieldsValues = {
    nomeStrada: '',
    numeroCivico: '',
    cap: '',
    comune: '',
    provincia: '',
    stato: '',
  };

  @track isEditable = true;
  @track isChecked = false;
  @track showInterprete = false;

  @track productTypeOptions = [
    { label: 'CASA', value: 'PUCASA' },
    { label: 'VEICOLO', value: 'PUVEICOLO' },
    { label: 'SALUTE', value: 'PUSALUTE' },
    { label: 'INFORTUNI', value: 'PUINFORTUNI' },
    { label: 'MOBILITÁ', value: 'PUMOBILITA' },
    { label: 'VIAGGI', value: 'PUVIAGGI' },
    { label: 'CANE E GATTO', value: 'PUPET' },
    { label: 'FAMIGLIA', value: 'PUFAMIGLIA' },
  ];

  @track tipoDiAbitazioniOptions = [
    { label: 'Appartamento', value: '1' },
    { label: 'Villa singola', value: '2' },
    { label: 'Villa schiera', value: '3' },
  ];

  @track tipoDiVeicoloOptions = [
    { label: 'Autovettura', value: '1' },
    { label: 'Autocarro', value: '2' },
    { label: 'Motocarro', value: '3' },
    { label: 'Ciclomotore Trasporto cose', value: '4' },
    { label: 'Motociclo', value: '5' },
    { label: 'Ciclomotore', value: '6' },
    { label: 'Quadriciclo', value: '7' },
    { label: 'Quadriciclo leggero', value: '8' },
  ];

  @track tipoDiSaluteOptions = [{ label: 'Salute', value: 'Salute' }];

  @track tipoDiInfotuniOptions = [{ label: 'Infortuni', value: 'Infortuni' }];

  @track tipoDiMobilitàOptions = [{ label: 'Mobilità', value: 'Mobilità' }];

  @track tipoDiViaggiOptions = [
    { label: 'ITALIA', value: '1101' },
    { label: 'FRANCIA', value: '1102' },
    { label: 'SPAGNA', value: '1103' },
  ];

  @track tipoDiPet = [
    { label: 'CANE', value: '1' },
    { label: 'GATTO', value: '2' },
  ];

  @track tipoDiEtaPet = [
    { label: '0-3 MESI', value: '1' },
    { label: '3 MESI - 1 ANNO', value: '2' },
    { label: '2 ANNI', value: '3' },
    { label: '3 ANNI', value: '4' },
    { label: '4 ANNI', value: '5' },
    { label: '5 ANNI', value: '6' },
    { label: '6 ANNI', value: '7' },
    { label: '7 ANNI', value: '8' },
    { label: '8 ANNI', value: '9' },
    { label: '9 ANNI', value: '10' },
    { label: '10 ANNI', value: '11' },
    { label: '10+ ANNI', value: '12' },
  ];

  initializeFieldsValues() {
    this.fieldsValues = Object.keys(this.request.referencesToUpdate).reduce((acc, key) => {
      let fieldName = key.split('.').at(-1);
      fieldName = fieldName.charAt(0).toLowerCase() + fieldName.slice(1);
      acc[fieldName] = this.request.referencesToUpdate[key] || '';
      return acc;
    }, {});
  }

  // TYPE REQUEST MOCK
  handleProductTypeChange(event) {
    const productType = event.target.value;
    this.isEditable = true;

    // TODO migliorare aggiornamento valori campi senza mock
    this.fieldsValues = {};
    switch (productType) {
      case 'PUCASA':
        this.request = {
          action: 'CREATE',
          env: 'UNICO',
          productType: 'PUCASA',
          captchaToken:
            'qMXibOi73POnQEaFlOTgcmljgSiMRmd4ZJ2KUE5O0JLh0TEvD3fKFd2OOD4dDP8oNkb6YVpAJnRap679IULJ2CdmyjhM4g6H-KCGeiUCI93qEsDbBtt0qRWuAM7aAwlENOHu88TdCLVx3WD2U2X12Y3ubC3_BFz1CZjcorDtGxYbQ4YsYkzXoy00dplZdpASxEF0ukLWCAULacXAPEBsV8klQ81SypVqFenbTAxHEqmGla572OfQIDzlBcXo39isfkAbnRC6xFl5woR57-vueYHDSdkJ4iByyiAUa5BhCjBtm_36lJ0OohRawfkxjcpi0oekI2xWRl5MyQGNFUFHNJ3TXkwLYb7M6B4XH2dr-utukyJvm708dVLdeXntg9kUBmYirVSTBKaswslNC7Tl2pDIyXhlKRKqRKGUe1EcIHmZQcwdjKxc29mGO-0pHNe0UxgU1d4GNMDL_5puwfSJJwIdQV4QdffdUciVI15iNAcT1mW0VKFljU_-tBY46ys4wjR7uyPZ8bJGPhkOvoNhlT9L7XZKggIuECrkrHQFQH2bslOdDv1UCcxKLooqQmpDpkVDNohwvsAOQv_9DdC_rP6IhfvtrsGnHsl6jLBqzY4JCj-GanuGXa_9_uy53TKQRk1__PYhJAeNuXfPS4uIKG6orUMzOxJYxGtClwNHBSltFZK5tdtYFTmNW9rtwW6HToNk5HIAyhYv7iowqDzVCzTCdPgrQAYZWT3iEOcd05ps-g153fbP8_fdMNn7DfX6DE2uDDf7Mxig8oYL4TGdDk0TFf_SyZDGX_X_YIS8J2LRqSCt8cxcE_qlorL6Jfx5_D1CO9zN9nRqWrFGW4xzEjuKkYbWPPPvjxwMOtoAUXm1KWHKgpU1_Tr9xTkBXO_Bml4huzPOXZ9_u6qX3Dbwbct5-PpXQC6LiEP9Wwc5BDbuVIONF3SI3xBDNXStfVseKR6pxCpMyDj9ejVEtnCMqERSlPwGpJdzBc1hegnYYrj06eh5Jd6aDM4g0a-L_mHflq4CJXC-jY9_OD5fgOm6WolF7yo9xtMIEOcZUV7Oh3IgnDJY-2FaVvJlmBEHBjiMkwm_d4a1lQfQbNgeVLzDWKkcHgg2lWTpzMik_ieU6tWtTbiCFKqsh7Zn-dIArSEWQXu_rofvENVQdoQF7UAcw33Qt1YD3lZLWEPTELW_3VeJYffJ-SQI29VNTbbTNMR6oUiaEPnYu6CgN8ZhIb7wDgmGAOaPSFQRFDwHlt2ryY7Xi9yjMeLoSIIFPvovVKo2aWlhDe5AvzQEfp8RQdVWwEP5B2rKkqsFd6o9CaIQLu-OD0Ess82mvbklGCz7QNPU3U4563ahVMq1gdPphXzpzUV88S69CZxqEo68tC0I5l8fV_wuDau7YDYLVh3SguAis5gwUAptddVPE_MPYFQSXwhhcnhaUBL3QvyilTt0ldI74rPG16AYQlhB6Gqu47UGXDHOm1N9kxhLWnYoXhTIkLspHT7u69YH28Hi670tx0jaLZovG8is7iUi1PFSuvf2amb7yfePZ22ubU_Dk0AT4xvBNYyw',
          referencesToUpdate: {
            'Ambito.Bene.Casa.TipologiaAbitazione': '1',
            'Ambito.Bene.Casa.Indirizzo.NomeStrada': 'Via Larga',
            'Ambito.Bene.Casa.Indirizzo.NumeroCivico': '12',
            'Ambito.Bene.Casa.Indirizzo.Comune': 'Milano',
            'Ambito.Bene.Casa.Indirizzo.Provincia': 'MI',
            'Ambito.Bene.Casa.Indirizzo.Cap': '20122',
            'Ambito.Bene.Casa.Indirizzo.Stato': 'Italia',
            'Ambito.Bene.Casa.Indirizzo.CodiceCatastaleComune': 'F205',
            'Ambito.Proprietario.TipoPersona': 'PF',
            'Contraente.Contatti.Email': '',
            'Ambito.DataDecorrenza': '16/05/2025',
          },
        };
        break;
      case 'PUVEICOLO':
        this.request = {
          action: 'CREATE',
          productType: 'PUVEICOLO',
          captchaToken:
            'qMXibOi73POnQEaFlOTgcmljgSiMRmd4ZJ2KUE5O0JLh0TEvD3fKFd2OOD4dDP8oNkb6YVpAJnRap679IULJ2CdmyjhM4g6H-KCGeiUCI93qEsDbBtt0qRWuAM7aAwlENOHu88TdCLVx3WD2U2X12Y3ubC3_BFz1CZjcorDtGxYbQ4YsYkzXoy00dplZdpASxEF0ukLWCAULacXAPEBsV8klQ81SypVqFenbTAxHEqmGla572OfQIDzlBcXo39isfkAbnRC6xFl5woR57-vueYHDSdkJ4iByyiAUa5BhCjBtm_36lJ0OohRawfkxjcpi0oekI2xWRl5MyQGNFUFHNJ3TXkwLYb7M6B4XH2dr-utukyJvm708dVLdeXntg9kUBmYirVSTBKaswslNC7Tl2pDIyXhlKRKqRKGUe1EcIHmZQcwdjKxc29mGO-0pHNe0UxgU1d4GNMDL_5puwfSJJwIdQV4QdffdUciVI15iNAcT1mW0VKFljU_-tBY46ys4wjR7uyPZ8bJGPhkOvoNhlT9L7XZKggIuECrkrHQFQH2bslOdDv1UCcxKLooqQmpDpkVDNohwvsAOQv_9DdC_rP6IhfvtrsGnHsl6jLBqzY4JCj-GanuGXa_9_uy53TKQRk1__PYhJAeNuXfPS4uIKG6orUMzOxJYxGtClwNHBSltFZK5tdtYFTmNW9rtwW6HToNk5HIAyhYv7iowqDzVCzTCdPgrQAYZWT3iEOcd05ps-g153fbP8_fdMNn7DfX6DE2uDDf7Mxig8oYL4TGdDk0TFf_SyZDGX_X_YIS8J2LRqSCt8cxcE_qlorL6Jfx5_D1CO9zN9nRqWrFGW4xzEjuKkYbWPPPvjxwMOtoAUXm1KWHKgpU1_Tr9xTkBXO_Bml4huzPOXZ9_u6qX3Dbwbct5-PpXQC6LiEP9Wwc5BDbuVIONF3SI3xBDNXStfVseKR6pxCpMyDj9ejVEtnCMqERSlPwGpJdzBc1hegnYYrj06eh5Jd6aDM4g0a-L_mHflq4CJXC-jY9_OD5fgOm6WolF7yo9xtMIEOcZUV7Oh3IgnDJY-2FaVvJlmBEHBjiMkwm_d4a1lQfQbNgeVLzDWKkcHgg2lWTpzMik_ieU6tWtTbiCFKqsh7Zn-dIArSEWQXu_rofvENVQdoQF7UAcw33Qt1YD3lZLWEPTELW_3VeJYffJ-SQI29VNTbbTNMR6oUiaEPnYu6CgN8ZhIb7wDgmGAOaPSFQRFDwHlt2ryY7Xi9yjMeLoSIIFPvovVKo2aWlhDe5AvzQEfp8RQdVWwEP5B2rKkqsFd6o9CaIQLu-OD0Ess82mvbklGCz7QNPU3U4563ahVMq1gdPphXzpzUV88S69CZxqEo68tC0I5l8fV_wuDau7YDYLVh3SguAis5gwUAptddVPE_MPYFQSXwhhcnhaUBL3QvyilTt0ldI74rPG16AYQlhB6Gqu47UGXDHOm1N9kxhLWnYoXhTIkLspHT7u69YH28Hi670tx0jaLZovG8is7iUi1PFSuvf2amb7yfePZ22ubU_Dk0AT4xvBNYyw',
          referencesToUpdate: {
            'Ambito.Bene.Auto.TipoVeicolo': '1',
            'Ambito.Bene.Auto.Targa': '',
            'Ambito.Bene.Auto.SenzaTarga': true,
            'Ambito.Proprietario.DataDiNascita': '01/01/2000',
            'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo': '',
            'Ambito.Proprietario.Residenza.NomeStrada': 'Via Larga',
            'Ambito.Proprietario.Residenza.NumeroCivico': '12',
            'Ambito.Proprietario.Residenza.Comune': 'Milano',
            'Ambito.Proprietario.Residenza.Provincia': 'MI',
            'Ambito.Proprietario.Residenza.Cap': '20122',
            'Ambito.Proprietario.Residenza.Stato': 'Italia',
            'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'F205',
            'Ambito.Proprietario.TipoPersona': 'PF',
            'Contraente.Contatti.Email': '',
            'Ambito.DataDecorrenza': '13/05/2025',
          },
          env: 'UNICO',
        };
        break;
      case 'PUSALUTE':
        this.request = {
          action: 'CREATE',
          env: 'UNICO',
          productType: 'PUSALUTE',
          captchaToken:
            'qMXibOi73POnQEaFlOTgcmljgSiMRmd4ZJ2KUE5O0JLh0TEvD3fKFd2OOD4dDP8oNkb6YVpAJnRap679IULJ2CdmyjhM4g6H-KCGeiUCI93qEsDbBtt0qRWuAM7aAwlENOHu88TdCLVx3WD2U2X12Y3ubC3_BFz1CZjcorDtGxYbQ4YsYkzXoy00dplZdpASxEF0ukLWCAULacXAPEBsV8klQ81SypVqFenbTAxHEqmGla572OfQIDzlBcXo39isfkAbnRC6xFl5woR57-vueYHDSdkJ4iByyiAUa5BhCjBtm_36lJ0OohRawfkxjcpi0oekI2xWRl5MyQGNFUFHNJ3TXkwLYb7M6B4XH2dr-utukyJvm708dVLdeXntg9kUBmYirVSTBKaswslNC7Tl2pDIyXhlKRKqRKGUe1EcIHmZQcwdjKxc29mGO-0pHNe0UxgU1d4GNMDL_5puwfSJJwIdQV4QdffdUciVI15iNAcT1mW0VKFljU_-tBY46ys4wjR7uyPZ8bJGPhkOvoNhlT9L7XZKggIuECrkrHQFQH2bslOdDv1UCcxKLooqQmpDpkVDNohwvsAOQv_9DdC_rP6IhfvtrsGnHsl6jLBqzY4JCj-GanuGXa_9_uy53TKQRk1__PYhJAeNuXfPS4uIKG6orUMzOxJYxGtClwNHBSltFZK5tdtYFTmNW9rtwW6HToNk5HIAyhYv7iowqDzVCzTCdPgrQAYZWT3iEOcd05ps-g153fbP8_fdMNn7DfX6DE2uDDf7Mxig8oYL4TGdDk0TFf_SyZDGX_X_YIS8J2LRqSCt8cxcE_qlorL6Jfx5_D1CO9zN9nRqWrFGW4xzEjuKkYbWPPPvjxwMOtoAUXm1KWHKgpU1_Tr9xTkBXO_Bml4huzPOXZ9_u6qX3Dbwbct5-PpXQC6LiEP9Wwc5BDbuVIONF3SI3xBDNXStfVseKR6pxCpMyDj9ejVEtnCMqERSlPwGpJdzBc1hegnYYrj06eh5Jd6aDM4g0a-L_mHflq4CJXC-jY9_OD5fgOm6WolF7yo9xtMIEOcZUV7Oh3IgnDJY-2FaVvJlmBEHBjiMkwm_d4a1lQfQbNgeVLzDWKkcHgg2lWTpzMik_ieU6tWtTbiCFKqsh7Zn-dIArSEWQXu_rofvENVQdoQF7UAcw33Qt1YD3lZLWEPTELW_3VeJYffJ-SQI29VNTbbTNMR6oUiaEPnYu6CgN8ZhIb7wDgmGAOaPSFQRFDwHlt2ryY7Xi9yjMeLoSIIFPvovVKo2aWlhDe5AvzQEfp8RQdVWwEP5B2rKkqsFd6o9CaIQLu-OD0Ess82mvbklGCz7QNPU3U4563ahVMq1gdPphXzpzUV88S69CZxqEo68tC0I5l8fV_wuDau7YDYLVh3SguAis5gwUAptddVPE_MPYFQSXwhhcnhaUBL3QvyilTt0ldI74rPG16AYQlhB6Gqu47UGXDHOm1N9kxhLWnYoXhTIkLspHT7u69YH28Hi670tx0jaLZovG8is7iUi1PFSuvf2amb7yfePZ22ubU_Dk0AT4xvBNYyw',
          referencesToUpdate: {
            'Ambito.DataDecorrenza': '16/04/2026',
            'Ambito.Proprietario.DataDiNascita': '',
            'Ambito.Proprietario.Professione': '9740',
            'Ambito.Proprietario.TipoOccupazione': 'D',
            'Ambito.Proprietario.Residenza.NomeStrada': 'via delle Mercede',
            'Ambito.Proprietario.Residenza.NumeroCivico': '37',
            'Ambito.Proprietario.Residenza.Comune': 'Roma',
            'Ambito.Proprietario.Residenza.Provincia': 'RM',
            'Ambito.Proprietario.Residenza.Cap': '00187',
            'Ambito.Proprietario.Residenza.Stato': 'Italia',
            'Ambito.Proprietario.TipoPersona': 'PF',
            'Contraente.CodiceFiscale': '',
            'Contraente.Contatti.Email': '',
            'GestioneProcesso.IdContrattoPU': '',
            'GestioneProcesso.OrigineQuotazione': '',
          },
        };
        break;
      case 'PUINFORTUNI':
        this.request = {
          action: 'CREATE',
          env: 'UNICO',
          productType: 'PUINFORTUNI',
          captchaToken:
            'qMXibOi73POnQEaFlOTgcmljgSiMRmd4ZJ2KUE5O0JLh0TEvD3fKFd2OOD4dDP8oNkb6YVpAJnRap679IULJ2CdmyjhM4g6H-KCGeiUCI93qEsDbBtt0qRWuAM7aAwlENOHu88TdCLVx3WD2U2X12Y3ubC3_BFz1CZjcorDtGxYbQ4YsYkzXoy00dplZdpASxEF0ukLWCAULacXAPEBsV8klQ81SypVqFenbTAxHEqmGla572OfQIDzlBcXo39isfkAbnRC6xFl5woR57-vueYHDSdkJ4iByyiAUa5BhCjBtm_36lJ0OohRawfkxjcpi0oekI2xWRl5MyQGNFUFHNJ3TXkwLYb7M6B4XH2dr-utukyJvm708dVLdeXntg9kUBmYirVSTBKaswslNC7Tl2pDIyXhlKRKqRKGUe1EcIHmZQcwdjKxc29mGO-0pHNe0UxgU1d4GNMDL_5puwfSJJwIdQV4QdffdUciVI15iNAcT1mW0VKFljU_-tBY46ys4wjR7uyPZ8bJGPhkOvoNhlT9L7XZKggIuECrkrHQFQH2bslOdDv1UCcxKLooqQmpDpkVDNohwvsAOQv_9DdC_rP6IhfvtrsGnHsl6jLBqzY4JCj-GanuGXa_9_uy53TKQRk1__PYhJAeNuXfPS4uIKG6orUMzOxJYxGtClwNHBSltFZK5tdtYFTmNW9rtwW6HToNk5HIAyhYv7iowqDzVCzTCdPgrQAYZWT3iEOcd05ps-g153fbP8_fdMNn7DfX6DE2uDDf7Mxig8oYL4TGdDk0TFf_SyZDGX_X_YIS8J2LRqSCt8cxcE_qlorL6Jfx5_D1CO9zN9nRqWrFGW4xzEjuKkYbWPPPvjxwMOtoAUXm1KWHKgpU1_Tr9xTkBXO_Bml4huzPOXZ9_u6qX3Dbwbct5-PpXQC6LiEP9Wwc5BDbuVIONF3SI3xBDNXStfVseKR6pxCpMyDj9ejVEtnCMqERSlPwGpJdzBc1hegnYYrj06eh5Jd6aDM4g0a-L_mHflq4CJXC-jY9_OD5fgOm6WolF7yo9xtMIEOcZUV7Oh3IgnDJY-2FaVvJlmBEHBjiMkwm_d4a1lQfQbNgeVLzDWKkcHgg2lWTpzMik_ieU6tWtTbiCFKqsh7Zn-dIArSEWQXu_rofvENVQdoQF7UAcw33Qt1YD3lZLWEPTELW_3VeJYffJ-SQI29VNTbbTNMR6oUiaEPnYu6CgN8ZhIb7wDgmGAOaPSFQRFDwHlt2ryY7Xi9yjMeLoSIIFPvovVKo2aWlhDe5AvzQEfp8RQdVWwEP5B2rKkqsFd6o9CaIQLu-OD0Ess82mvbklGCz7QNPU3U4563ahVMq1gdPphXzpzUV88S69CZxqEo68tC0I5l8fV_wuDau7YDYLVh3SguAis5gwUAptddVPE_MPYFQSXwhhcnhaUBL3QvyilTt0ldI74rPG16AYQlhB6Gqu47UGXDHOm1N9kxhLWnYoXhTIkLspHT7u69YH28Hi670tx0jaLZovG8is7iUi1PFSuvf2amb7yfePZ22ubU_Dk0AT4xvBNYyw',
          referencesToUpdate: {
            'Ambito.DataDecorrenza': '16/04/2026',
            'Ambito.Proprietario.DataDiNascita': '',
            'Ambito.Proprietario.Professione': '9740',
            'Ambito.Proprietario.TipoOccupazione': 'D',
            'Ambito.Proprietario.StatusFamiliare': '0',
            'Ambito.Proprietario.Residenza.NomeStrada': 'via Tortona',
            'Ambito.Proprietario.Residenza.NumeroCivico': '58',
            'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'F205',
            'Ambito.Proprietario.Residenza.Comune': 'Milano',
            'Ambito.Proprietario.Residenza.Provincia': 'MI',
            'Ambito.Proprietario.Residenza.Cap': '20144',
            'Ambito.Proprietario.Residenza.Stato': 'Italia',
            'Ambito.Proprietario.TipoPersona': 'PF',
            'Contraente.CodiceFiscale': '',
            'Contraente.Contatti.Email': '',
            'GestioneProcesso.FlagSessioneAttiva': false,
            'GestioneProcesso.IdContrattoPU': '',
            'GestioneProcesso.OrigineQuotazione': '',
          },
        };
        break;
      case 'PUMOBILITA':
        this.request = {
          action: 'CREATE',
          env: 'UNICO',
          productType: 'PUMOBILITA',
          captchaToken:
            'qMXibOi73POnQEaFlOTgcmljgSiMRmd4ZJ2KUE5O0JLh0TEvD3fKFd2OOD4dDP8oNkb6YVpAJnRap679IULJ2CdmyjhM4g6H-KCGeiUCI93qEsDbBtt0qRWuAM7aAwlENOHu88TdCLVx3WD2U2X12Y3ubC3_BFz1CZjcorDtGxYbQ4YsYkzXoy00dplZdpASxEF0ukLWCAULacXAPEBsV8klQ81SypVqFenbTAxHEqmGla572OfQIDzlBcXo39isfkAbnRC6xFl5woR57-vueYHDSdkJ4iByyiAUa5BhCjBtm_36lJ0OohRawfkxjcpi0oekI2xWRl5MyQGNFUFHNJ3TXkwLYb7M6B4XH2dr-utukyJvm708dVLdeXntg9kUBmYirVSTBKaswslNC7Tl2pDIyXhlKRKqRKGUe1EcIHmZQcwdjKxc29mGO-0pHNe0UxgU1d4GNMDL_5puwfSJJwIdQV4QdffdUciVI15iNAcT1mW0VKFljU_-tBY46ys4wjR7uyPZ8bJGPhkOvoNhlT9L7XZKggIuECrkrHQFQH2bslOdDv1UCcxKLooqQmpDpkVDNohwvsAOQv_9DdC_rP6IhfvtrsGnHsl6jLBqzY4JCj-GanuGXa_9_uy53TKQRk1__PYhJAeNuXfPS4uIKG6orUMzOxJYxGtClwNHBSltFZK5tdtYFTmNW9rtwW6HToNk5HIAyhYv7iowqDzVCzTCdPgrQAYZWT3iEOcd05ps-g153fbP8_fdMNn7DfX6DE2uDDf7Mxig8oYL4TGdDk0TFf_SyZDGX_X_YIS8J2LRqSCt8cxcE_qlorL6Jfx5_D1CO9zN9nRqWrFGW4xzEjuKkYbWPPPvjxwMOtoAUXm1KWHKgpU1_Tr9xTkBXO_Bml4huzPOXZ9_u6qX3Dbwbct5-PpXQC6LiEP9Wwc5BDbuVIONF3SI3xBDNXStfVseKR6pxCpMyDj9ejVEtnCMqERSlPwGpJdzBc1hegnYYrj06eh5Jd6aDM4g0a-L_mHflq4CJXC-jY9_OD5fgOm6WolF7yo9xtMIEOcZUV7Oh3IgnDJY-2FaVvJlmBEHBjiMkwm_d4a1lQfQbNgeVLzDWKkcHgg2lWTpzMik_ieU6tWtTbiCFKqsh7Zn-dIArSEWQXu_rofvENVQdoQF7UAcw33Qt1YD3lZLWEPTELW_3VeJYffJ-SQI29VNTbbTNMR6oUiaEPnYu6CgN8ZhIb7wDgmGAOaPSFQRFDwHlt2ryY7Xi9yjMeLoSIIFPvovVKo2aWlhDe5AvzQEfp8RQdVWwEP5B2rKkqsFd6o9CaIQLu-OD0Ess82mvbklGCz7QNPU3U4563ahVMq1gdPphXzpzUV88S69CZxqEo68tC0I5l8fV_wuDau7YDYLVh3SguAis5gwUAptddVPE_MPYFQSXwhhcnhaUBL3QvyilTt0ldI74rPG16AYQlhB6Gqu47UGXDHOm1N9kxhLWnYoXhTIkLspHT7u69YH28Hi670tx0jaLZovG8is7iUi1PFSuvf2amb7yfePZ22ubU_Dk0AT4xvBNYyw',
          referencesToUpdate: {
            'Ambito.Proprietario.DataDiNascita': '',
            'Ambito.Proprietario.Residenza.NomeStrada': '',
            'Ambito.Proprietario.Residenza.NumeroCivico': '',
            'Ambito.Proprietario.Residenza.Comune': '',
            'Ambito.Proprietario.Residenza.Provincia': '',
            'Ambito.Proprietario.Residenza.Cap': '',
            'Ambito.Proprietario.Residenza.Stato': '',
          },
        };
        break;
      case 'PUVIAGGI':
        this.request = {
          action: 'CREATE',
          env: 'UNICO',
          productType: 'PUVIAGGI',
          captchaToken:
            'qMXibOi73POnQEaFlOTgcmljgSiMRmd4ZJ2KUE5O0JLh0TEvD3fKFd2OOD4dDP8oNkb6YVpAJnRap679IULJ2CdmyjhM4g6H-KCGeiUCI93qEsDbBtt0qRWuAM7aAwlENOHu88TdCLVx3WD2U2X12Y3ubC3_BFz1CZjcorDtGxYbQ4YsYkzXoy00dplZdpASxEF0ukLWCAULacXAPEBsV8klQ81SypVqFenbTAxHEqmGla572OfQIDzlBcXo39isfkAbnRC6xFl5woR57-vueYHDSdkJ4iByyiAUa5BhCjBtm_36lJ0OohRawfkxjcpi0oekI2xWRl5MyQGNFUFHNJ3TXkwLYb7M6B4XH2dr-utukyJvm708dVLdeXntg9kUBmYirVSTBKaswslNC7Tl2pDIyXhlKRKqRKGUe1EcIHmZQcwdjKxc29mGO-0pHNe0UxgU1d4GNMDL_5puwfSJJwIdQV4QdffdUciVI15iNAcT1mW0VKFljU_-tBY46ys4wjR7uyPZ8bJGPhkOvoNhlT9L7XZKggIuECrkrHQFQH2bslOdDv1UCcxKLooqQmpDpkVDNohwvsAOQv_9DdC_rP6IhfvtrsGnHsl6jLBqzY4JCj-GanuGXa_9_uy53TKQRk1__PYhJAeNuXfPS4uIKG6orUMzOxJYxGtClwNHBSltFZK5tdtYFTmNW9rtwW6HToNk5HIAyhYv7iowqDzVCzTCdPgrQAYZWT3iEOcd05ps-g153fbP8_fdMNn7DfX6DE2uDDf7Mxig8oYL4TGdDk0TFf_SyZDGX_X_YIS8J2LRqSCt8cxcE_qlorL6Jfx5_D1CO9zN9nRqWrFGW4xzEjuKkYbWPPPvjxwMOtoAUXm1KWHKgpU1_Tr9xTkBXO_Bml4huzPOXZ9_u6qX3Dbwbct5-PpXQC6LiEP9Wwc5BDbuVIONF3SI3xBDNXStfVseKR6pxCpMyDj9ejVEtnCMqERSlPwGpJdzBc1hegnYYrj06eh5Jd6aDM4g0a-L_mHflq4CJXC-jY9_OD5fgOm6WolF7yo9xtMIEOcZUV7Oh3IgnDJY-2FaVvJlmBEHBjiMkwm_d4a1lQfQbNgeVLzDWKkcHgg2lWTpzMik_ieU6tWtTbiCFKqsh7Zn-dIArSEWQXu_rofvENVQdoQF7UAcw33Qt1YD3lZLWEPTELW_3VeJYffJ-SQI29VNTbbTNMR6oUiaEPnYu6CgN8ZhIb7wDgmGAOaPSFQRFDwHlt2ryY7Xi9yjMeLoSIIFPvovVKo2aWlhDe5AvzQEfp8RQdVWwEP5B2rKkqsFd6o9CaIQLu-OD0Ess82mvbklGCz7QNPU3U4563ahVMq1gdPphXzpzUV88S69CZxqEo68tC0I5l8fV_wuDau7YDYLVh3SguAis5gwUAptddVPE_MPYFQSXwhhcnhaUBL3QvyilTt0ldI74rPG16AYQlhB6Gqu47UGXDHOm1N9kxhLWnYoXhTIkLspHT7u69YH28Hi670tx0jaLZovG8is7iUi1PFSuvf2amb7yfePZ22ubU_Dk0AT4xvBNYyw',
          referencesToUpdate: {
            'Ambito.Bene.Viaggio.PaeseDestinazione': '',
            'Ambito.Bene.Viaggio.NumeroViaggiatori': '1',
            'Ambito.Bene.Viaggio.DataViaggioAndata': '21/05/2026',
            'Ambito.Bene.Viaggio.DataViaggioRitorno': '23/05/2026',
          },
        };
        break;
      case 'PUPET':
        this.request = {
          action: 'CREATE',
          productType: 'PUPET',
          captchaToken:
            'qMXibOi73POnQEaFlOTgcmljgSiMRmd4ZJ2KUE5O0JLh0TEvD3fKFd2OOD4dDP8oNkb6YVpAJnRap679IULJ2CdmyjhM4g6H-KCGeiUCI93qEsDbBtt0qRWuAM7aAwlENOHu88TdCLVx3WD2U2X12Y3ubC3_BFz1CZjcorDtGxYbQ4YsYkzXoy00dplZdpASxEF0ukLWCAULacXAPEBsV8klQ81SypVqFenbTAxHEqmGla572OfQIDzlBcXo39isfkAbnRC6xFl5woR57-vueYHDSdkJ4iByyiAUa5BhCjBtm_36lJ0OohRawfkxjcpi0oekI2xWRl5MyQGNFUFHNJ3TXkwLYb7M6B4XH2dr-utukyJvm708dVLdeXntg9kUBmYirVSTBKaswslNC7Tl2pDIyXhlKRKqRKGUe1EcIHmZQcwdjKxc29mGO-0pHNe0UxgU1d4GNMDL_5puwfSJJwIdQV4QdffdUciVI15iNAcT1mW0VKFljU_-tBY46ys4wjR7uyPZ8bJGPhkOvoNhlT9L7XZKggIuECrkrHQFQH2bslOdDv1UCcxKLooqQmpDpkVDNohwvsAOQv_9DdC_rP6IhfvtrsGnHsl6jLBqzY4JCj-GanuGXa_9_uy53TKQRk1__PYhJAeNuXfPS4uIKG6orUMzOxJYxGtClwNHBSltFZK5tdtYFTmNW9rtwW6HToNk5HIAyhYv7iowqDzVCzTCdPgrQAYZWT3iEOcd05ps-g153fbP8_fdMNn7DfX6DE2uDDf7Mxig8oYL4TGdDk0TFf_SyZDGX_X_YIS8J2LRqSCt8cxcE_qlorL6Jfx5_D1CO9zN9nRqWrFGW4xzEjuKkYbWPPPvjxwMOtoAUXm1KWHKgpU1_Tr9xTkBXO_Bml4huzPOXZ9_u6qX3Dbwbct5-PpXQC6LiEP9Wwc5BDbuVIONF3SI3xBDNXStfVseKR6pxCpMyDj9ejVEtnCMqERSlPwGpJdzBc1hegnYYrj06eh5Jd6aDM4g0a-L_mHflq4CJXC-jY9_OD5fgOm6WolF7yo9xtMIEOcZUV7Oh3IgnDJY-2FaVvJlmBEHBjiMkwm_d4a1lQfQbNgeVLzDWKkcHgg2lWTpzMik_ieU6tWtTbiCFKqsh7Zn-dIArSEWQXu_rofvENVQdoQF7UAcw33Qt1YD3lZLWEPTELW_3VeJYffJ-SQI29VNTbbTNMR6oUiaEPnYu6CgN8ZhIb7wDgmGAOaPSFQRFDwHlt2ryY7Xi9yjMeLoSIIFPvovVKo2aWlhDe5AvzQEfp8RQdVWwEP5B2rKkqsFd6o9CaIQLu-OD0Ess82mvbklGCz7QNPU3U4563ahVMq1gdPphXzpzUV88S69CZxqEo68tC0I5l8fV_wuDau7YDYLVh3SguAis5gwUAptddVPE_MPYFQSXwhhcnhaUBL3QvyilTt0ldI74rPG16AYQlhB6Gqu47UGXDHOm1N9kxhLWnYoXhTIkLspHT7u69YH28Hi670tx0jaLZovG8is7iUi1PFSuvf2amb7yfePZ22ubU_Dk0AT4xvBNYyw',
          referencesToUpdate: {
            'Ambito.Bene.Pet.TipologiaAnimale': '1',
            'Ambito.Bene.Pet.Eta': '1',
            'Ambito.Proprietario.TipoPersona': 'PF',
            'Contraente.Contatti.Email': '',
          },
          env: 'UNICO',
        };
        break;
      case 'PUFAMIGLIA':
        this.request = {
          action: 'CREATE',
          env: 'UNICO',
          productType: 'PUFAMIGLIA',
          captchaToken:
            'qMXibOi73POnQEaFlOTgcmljgSiMRmd4ZJ2KUE5O0JLh0TEvD3fKFd2OOD4dDP8oNkb6YVpAJnRap679IULJ2CdmyjhM4g6H-KCGeiUCI93qEsDbBtt0qRWuAM7aAwlENOHu88TdCLVx3WD2U2X12Y3ubC3_BFz1CZjcorDtGxYbQ4YsYkzXoy00dplZdpASxEF0ukLWCAULacXAPEBsV8klQ81SypVqFenbTAxHEqmGla572OfQIDzlBcXo39isfkAbnRC6xFl5woR57-vueYHDSdkJ4iByyiAUa5BhCjBtm_36lJ0OohRawfkxjcpi0oekI2xWRl5MyQGNFUFHNJ3TXkwLYb7M6B4XH2dr-utukyJvm708dVLdeXntg9kUBmYirVSTBKaswslNC7Tl2pDIyXhlKRKqRKGUe1EcIHmZQcwdjKxc29mGO-0pHNe0UxgU1d4GNMDL_5puwfSJJwIdQV4QdffdUciVI15iNAcT1mW0VKFljU_-tBY46ys4wjR7uyPZ8bJGPhkOvoNhlT9L7XZKggIuECrkrHQFQH2bslOdDv1UCcxKLooqQmpDpkVDNohwvsAOQv_9DdC_rP6IhfvtrsGnHsl6jLBqzY4JCj-GanuGXa_9_uy53TKQRk1__PYhJAeNuXfPS4uIKG6orUMzOxJYxGtClwNHBSltFZK5tdtYFTmNW9rtwW6HToNk5HIAyhYv7iowqDzVCzTCdPgrQAYZWT3iEOcd05ps-g153fbP8_fdMNn7DfX6DE2uDDf7Mxig8oYL4TGdDk0TFf_SyZDGX_X_YIS8J2LRqSCt8cxcE_qlorL6Jfx5_D1CO9zN9nRqWrFGW4xzEjuKkYbWPPPvjxwMOtoAUXm1KWHKgpU1_Tr9xTkBXO_Bml4huzPOXZ9_u6qX3Dbwbct5-PpXQC6LiEP9Wwc5BDbuVIONF3SI3xBDNXStfVseKR6pxCpMyDj9ejVEtnCMqERSlPwGpJdzBc1hegnYYrj06eh5Jd6aDM4g0a-L_mHflq4CJXC-jY9_OD5fgOm6WolF7yo9xtMIEOcZUV7Oh3IgnDJY-2FaVvJlmBEHBjiMkwm_d4a1lQfQbNgeVLzDWKkcHgg2lWTpzMik_ieU6tWtTbiCFKqsh7Zn-dIArSEWQXu_rofvENVQdoQF7UAcw33Qt1YD3lZLWEPTELW_3VeJYffJ-SQI29VNTbbTNMR6oUiaEPnYu6CgN8ZhIb7wDgmGAOaPSFQRFDwHlt2ryY7Xi9yjMeLoSIIFPvovVKo2aWlhDe5AvzQEfp8RQdVWwEP5B2rKkqsFd6o9CaIQLu-OD0Ess82mvbklGCz7QNPU3U4563ahVMq1gdPphXzpzUV88S69CZxqEo68tC0I5l8fV_wuDau7YDYLVh3SguAis5gwUAptddVPE_MPYFQSXwhhcnhaUBL3QvyilTt0ldI74rPG16AYQlhB6Gqu47UGXDHOm1N9kxhLWnYoXhTIkLspHT7u69YH28Hi670tx0jaLZovG8is7iUi1PFSuvf2amb7yfePZ22ubU_Dk0AT4xvBNYyw',
          referencesToUpdate: {
            'Ambito.Bene.Famiglia.Immobile.TipologiaAbitazione': '',
            'Ambito.Proprietario.Residenza.NomeStrada': '',
            'Ambito.Proprietario.Residenza.NumeroCivico': '',
            'Ambito.Proprietario.Residenza.Comune': '',
            'Ambito.Proprietario.Residenza.Provincia': '',
            'Ambito.Proprietario.Residenza.Cap': '',
            'Ambito.Proprietario.Residenza.Stato': '',
            'Ambito.Proprietario.Residenza.CodiceCatastaleComune': '',
            'Ambito.Proprietario.TipoPersona': '',
          },
        };
        break;
    }
    this.request.productType = productType;
    this.request = { ...this.request };
    this.initializeFieldsValues();
  }

  handleInputChange(event) {
    const field = event.target.dataset.field;
    const value = event.target.value;
    this.request.referencesToUpdate[field] = value;
    this.request = { ...this.request };
  }

  productHandlers = {
    PUCASA: this.handleCasa.bind(this),
    PUVEICOLO: this.handleVeicolo.bind(this),
    PUSALUTE: this.handleSalute.bind(this),
    PUINFORTUNI: this.handleInfortuni.bind(this),
    PUMOBILITA: this.handleMobilita.bind(this),
    PUVIAGGI: this.handleViaggi.bind(this),
    PUPET: this.handlePet.bind(this),
    PUFAMIGLIA: this.handleFamiglia.bind(this),
  };

  handleDisambiguationFieldChange(event) {
    const productType = this.request.productType;
    const fieldName = event.target.name;
    const fieldValue = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    if (this.productHandlers[productType]) {
      this.productHandlers[productType](fieldName, fieldValue);
    } else {
      console.warn('Tipo di prodotto non gestito:', productType);
    }
  }

  // FIELDS HANDLERS HELPERS

  handleDataNascita(fieldName, fieldValue) {
    const tempDate = new Date(fieldValue).toLocaleDateString('it-IT');

    if (fieldName === 'dataNascita')
      this.request.referencesToUpdate['Ambito.Proprietario.DataDiNascita'] = tempDate;
  }

  // START OF FIELDS HANDLERS
  handleCasa(fieldName, fieldValue) {
    if (fieldName === 'confermaIndirizzo') {
      this.isEditable = !this.isEditable;
    } else if (fieldName === 'tipologiaAppartamento') {
      this.request.referencesToUpdate['Ambito.Bene.Casa.TipologiaAbitazione'] = fieldValue;
    } else {
      let fieldNameAccessor = fieldName.split('.').at(-1);
      fieldNameAccessor = fieldNameAccessor.charAt(0).toLowerCase() + fieldNameAccessor.slice(1);
      this.fieldsValues[fieldNameAccessor] = fieldValue;
      this.request.referencesToUpdate[fieldName] = fieldValue;
    }
  }

  handleVeicolo(fieldName, fieldValue) {
    if (fieldName === 'noTarga') {
      this.isChecked = fieldValue;
      this.request.referencesToUpdate['Ambito.Bene.Auto.Targa'] = '';
    } else if (fieldName === 'dataNascita') {
      this.handleDataNascita(fieldName, fieldValue);
    } else if (fieldName === 'confermaIndirizzo') {
      this.isEditable = !this.isEditable;
    } else if (fieldName === 'trasferisciMerito' && !fieldValue) {
      this.request.referencesToUpdate['Ambito.Bene.Auto.RecuperoClasseAltroVeicolo'] = '';
    } else {
      let fieldNameAccessor = fieldName.split('.').at(-1);
      fieldNameAccessor = fieldNameAccessor.charAt(0).toLowerCase() + fieldNameAccessor.slice(1);
      this.fieldsValues[fieldNameAccessor] = fieldValue;
      this.request.referencesToUpdate[fieldName] = fieldValue;
    }
  }

  handleSalute(fieldName, fieldValue) {
    if (fieldName === 'dataNascita') {
      this.handleDataNascita(fieldName, fieldValue);
    }
  }

  handleInfortuni(fieldName, fieldValue) {
    if (fieldName === 'dataNascita') {
      this.handleDataNascita(fieldName, fieldValue);
    }
  }

  handleMobilita(fieldName, fieldValue) {
    if (fieldName === 'dataNascita') {
      this.handleDataNascita(fieldName, fieldValue);
    } else {
      this.request.referencesToUpdate[fieldName] = fieldValue;
    }
  }

  handleViaggi(fieldName, fieldValue) {
    if (fieldName === 'tipologiaViaggi') {
      this.request.referencesToUpdate['Ambito.Bene.Viaggio.PaeseDestinazione'] = fieldValue;
    }
  }

  handlePet(fieldName, fieldValue) {
    if (fieldName === 'tipologiaPet') {
      this.request.referencesToUpdate['Ambito.Bene.Pet.TipologiaAnimale'] = fieldValue;
    } else if (fieldName === 'etaPet') {
      this.request.referencesToUpdate['Ambito.Bene.Pet.Eta'] = fieldValue;
    }
  }

  handleFamiglia(fieldName, fieldValue) {
    if (fieldName === 'tipologiaAppartamento') {
      this.request.referencesToUpdate['Ambito.Bene.Famiglia.Immobile.TipologiaAbitazione'] =
        fieldValue;
    } else {
      this.request.referencesToUpdate[fieldName] = fieldValue;
    }
  }

  // END OF FIELDS HANDLERS

  handleProceed() {
    this.showInterprete = true;
  }

  handleRetryEvent() {
    this.showInterprete = false;
  }

  get debugJSON() {
    return JSON.stringify(this.request, null, 2);
  }

  connectedCallback() {}
}
