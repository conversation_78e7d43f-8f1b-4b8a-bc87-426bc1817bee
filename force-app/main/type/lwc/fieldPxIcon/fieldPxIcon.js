import { fireEvent } from 'c/pubsub';
import { LightningElement, api, track } from 'lwc';
import { ICON_MAPPER, IMG_MOBILE, IMG_TABLET, IMG_DESKTOP, BASE_ASSET_URL } from './iconMapping';
import { utils } from 'c/utils';

export default class PxIcon extends LightningElement {
  _field;
  @track type = 'icon';
  @track imgMobile;
  @track imgTablet;
  @track imgDesktop;
  @api debug;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    this.updateImageUrls();
  }

  get isIcon() {
    return (this.field?.customAttributes?.type === 'icon') ||
      (!this.field?.customAttributes?.type && this.field?.control?.type === 'pxIcon');
  }

  get isImage() {
    return this.field?.customAttributes?.type === 'image';
  }

  get isLabelVisible() {
    return this.field.label && this.field.showLabel;
  }

  get resource() {
    return `${ICON_MAPPER[this.field?.customAttributes?.resource]} ${this.webResponsiveSize ? 'webResponsiveSize' : ''}`;
  }

  get componentClass() {
    return `pxIcon ${this.debug ? 'debug' : ''}`.trim();
  }


  get webResponsiveSize() {
    return utils.getCustomAttributeFromFieldUnsensitive(this.field, 'webResponsiveSize', undefined)
  }

  get responsiveSizeFormattedLabel() {
    let esito = '';

    if (this.webResponsiveSize) {
      const part = this.webResponsiveSize.split(' ');
      if (part.length === 3) {
        for (let i = 0; i < part.length; i++) {
          const size = part[i];
          const dimensione = size.substring(0, size.length - 1);
          const allineamento = size.substring(size.length - 1);

          const mapperTaglia = ['--desktop', '--tablet', '--mobile'];
          const mapperAllineamento = { 'L': '0 auto 0 0', 'C': '0 auto', 'R': '0 0 0 auto' };
          esito += `${mapperTaglia[i]}-size: ${dimensione}px; ${mapperTaglia[i]}-align: ${mapperAllineamento[allineamento]}; `;
        }
      }
    }

    return esito;
  }

  updateImageUrls() {
    if (this.isImage) {
      const resource = this.field?.customAttributes?.resource;

      const base = BASE_ASSET_URL;

      this.imgMobile = IMG_MOBILE[resource];
      this.imgTablet = IMG_TABLET[resource];
      this.imgDesktop = IMG_DESKTOP[resource];
      if (!this.imgMobile) {
        this.imgMobile = `${base}/NextAssets/interprete-pu/immagini-pu/Mobile/${resource}.png`;
      }
      if (!this.imgTablet) {
        this.imgTablet = `${base}/NextAssets/interprete-pu/immagini-pu/Tablet/${resource}.png`;
      }
      if (!this.imgDesktop) {
        this.imgDesktop = `${base}/NextAssets/interprete-pu/immagini-pu/Desktop/${resource}.png`;
      }
    }
  }

  handleClick(evt) {
    fireEvent('handleFieldClicked', {
      evt: evt,
      field: this.field,
      parentLayout: this.parentLayout
    });
  }
}
