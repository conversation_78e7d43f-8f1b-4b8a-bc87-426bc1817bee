.pxIcon {
  background: no-repeat;
  background-size: cover;
}

.webResponsiveSize {
  width: 40px !important;
  height: 40px !important;
  margin: 0 auto 0 0 !important;
}

@media (max-width: 599px) {
  .webResponsiveSize {
    width: var(--mobile-size) !important;
    height: var(--mobile-size) !important;
    margin: var(--mobile-align) !important;
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .webResponsiveSize {
    width: var(--tablet-size) !important;
    height: var(--tablet-size) !important;
    margin: var(--tablet-align) !important;
  }
}

@media (min-width: 1024px) {
  .webResponsiveSize {
    width: var(--desktop-size) !important;
    height: var(--desktop-size) !important;
    margin: var(--desktop-align) !important;
  }
}



.debug {
  border: 1px solid #e7a11f;
}

.temporaryLabel {
  color: #e7a11f;
  font-style: italic;
}

.icon-Freccia-sx {
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/icons/angle-right.svg");
  background-size: contain;
  background-repeat: no-repeat;
  cursor: pointer;
  transform: rotate(180deg);
  filter: brightness(0) invert(1);
}

.lightInfoPu {
  display: block;
  width: 24px;
  height: 24px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/LightInfo.svg");
  background-size: cover;
  cursor: pointer;
}

.mastercardIconPu {
  display: block;
  width: 28px;
  height: 18px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/icone-carte/MastercardIcon.svg");
  background-size: cover;
}

.visaIconPu {
  display: block;
  width: 28px;
  height: 18px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/icone-carte/VisaIcon.svg");
  background-size: cover;
}

.applepayIconPu {
  display: block;
  width: 28px;
  height: 18px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/icone-carte/ApplepayIcon.svg");
  background-size: cover;
}

.amazonpayIconPu {
  display: block;
  width: 28px;
  height: 18px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/icone-carte/AmazonpayIcon.svg");
  background-size: cover;
}

.satispayIconPu {
  display: block;
  width: 28px;
  height: 18px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/icone-carte/SatispayIcon.svg");
  background-size: cover;
}

.paypalIconPu {
  display: block;
  width: 28px;
  height: 18px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/icone-carte/PaypalIcon.svg");
  background-size: cover;
}

.tecnologiaIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/TecnologiaIcon.svg");
  background-size: cover;
}

.assistenzaVeicoloIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/AssistenzaVeicoloIcon.svg");
  background-size: cover;
}

.protezioneVeicoloIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/ProtezioneVeicoloIcon.svg");
  background-size: cover;
}

.infortuniDelConducenteIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/InfortuniDelConducenteIcon.svg");
  background-size: cover;
}

.sezioneTutelaLegaleFamigliaIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/SezioneTutelaLegaleFamigliaIcon.svg");
  background-size: cover;
}

.tutelaLegaleVeicoloIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/TutelaLegaleVeicoloIcon.svg");
  background-size: cover;
}

.lockIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/LockIcon.svg");
  background-size: cover;
}

.icon-Unica {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.icon-Unica::before {
  content: "";
  display: block;
  height: 100%;
  width: 100%;
  background-image: url("https://unipolsai.it/NextAssets/icons/icon-Unica2.svg");
  background-size: cover;
}

.icon-House-protection {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.icon-House-protection::before {
  content: "";
  display: block;
  height: 100%;
  width: 100%;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/HouseProtectionIcon.svg");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.pencilNegativePu {
  display: block;
  width: 40px;
  height: 40px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/PencilNegativeIcon.svg");
  background-size: cover;
  cursor: pointer;
}

.trashNegativePu {
  display: block;
  width: 40px;
  height: 40px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/TrashNegativeIcon.svg");
  background-size: cover;
  cursor: pointer;
}

.travelProtectionIconPu {
  display: block;
  width: 32px;
  height: 32px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/TravelProtectionIcon.svg");
  background-size: cover;
}

.autoRcaIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/AutoRcaIcon.svg");
  background-size: cover;
}

.successPu {
  display: block;
  width: 40px;
  height: 40px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/PolliceInSuIcon.svg");
  background-size: cover;
}

.icon-check-green {
  width: 20px;
  height: 20px;
}

.icon-check-green::before {
  cursor: pointer;
  content: "";
  position: absolute;
  background: url(/TpdPortalCommons/build/assets/icons-unibox-servizi-attivo.svg);
  background-size: cover;
  height: 20px;
  width: 20px;
}

.icon-warning {
  width: 40px;
  height: 40px;
}

.housePositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/HousePositiveIcon.svg");
  background-size: cover;
}

.familyPositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/FamilyPositiveIcon.svg");
  background-size: cover;
}

.viaggiPositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/ViaggiPositiveIcon.svg");
  background-size: cover;
}

.petPositiveIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/PetPositiveIcon.svg");
  background-size: cover;
}

.sezioneProtezioneCasaIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/SezioneProtezioneCasaIcon.svg");
  background-size: cover;
}

.sezioneFurtoIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/SezioneFurtoIcon.svg");
  background-size: cover;
}

.sezioneTerremotoAlluvioneIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/SezioneTerremotoAlluvioneIcon.svg");
  background-size: cover;
}

.sezioneFotovoltaicoIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/SezioneFotovoltaicoIcon.svg");
  background-size: cover;
}

.sezioneAssistenzaCasaIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/SezioneAssistenzaCasaIcon.svg");
  background-size: cover;
}

.presentIconPu {
  display: block;
  width: 32px;
  height: 32px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/PresentIcon.svg");
  background-size: cover;
}

.sezioneDanniATerziIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/SezioneDanniATerziIcon.svg");
  background-size: cover;
}

.sezioneTutelaLegaleMobilitaIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("https://unipolsai.it/NextAssets/interprete-pu/SezioneTutelaLegaleMobilitaIcon.svg");
  background-size: cover;
}

.sezioneAssitenzaCaneGattoIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url("/https://unipolsai.it/NextAssets/interprete-pu/SezioneAssistenzaCaneGattoIcon.svg");
  background-size: cover;
}

.icon-Pet-protection {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.icon-Pet-protection::before {
  content: "";
  display: block;
  background: url("https://unipolsai.it/NextAssets/icons/icon-Pet.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  height: 100%;
  width: 100%;
}

.icon-matita {
  height: 28px;
  width: 28px;
}

.icon-matita::before {
  cursor: pointer;
  content: "";
  position: absolute;
  background: url("https://www.unipol.it/NextAssets/icons/edit_matita.png");
  background-size: cover;
  width: 28px;
  height: 28px;
}

.icon-Attenzione-pieno {
  color: var(--alert-color);
  font-size: 60px;
  position: relative;
  margin-right: 5px;
}

.icon-Attenzione-pieno:before {
  content: "\e910";
}

.iconChiusuraPu {
  aspect-ratio: 1 / 1;
  display: block;
  position: relative;
  flex-shrink: 0;
  width: 32px;
  cursor: pointer;
}

@media (max-width: 768px) {

  /* Sostituire "#{$bkp_mobile_only}" con una dimensione media reale */
  .iconChiusuraPu {
    width: 24px;
  }
}

.iconChiusuraPu::after,
.iconChiusuraPu::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  width: 3px;
  height: 100%;
  background-color: #007bff;
  /* Sostituire "$blue-primary" con il colore reale */
  border-radius: 24px;
}

.iconChiusuraPu::after {
  transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
}

.iconChiusuraPu::before {
  transform: translateX(-50%) translateY(-50%) rotateZ(45deg);
}

.visibility-src-tablet,
.visibility-src-desktop {
  display: none;
}

.visibility-src-mobile {
  display: block;
}

@media (min-width: 768px) {

  .visibility-src-mobile,
  .visibility-src-desktop {
    display: none;
  }

  .visibility-src-tablet {
    display: block;
  }
}

@media (min-width: 1280px) {

  .visibility-src-mobile,
  .visibility-src-tablet {
    display: none;
  }

  .visibility-src-desktop {
    display: block;
  }
}

img {
  margin: 28px auto;
  width: 100%;
}