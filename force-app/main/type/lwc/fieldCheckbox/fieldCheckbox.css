
.debug {
  border: 1px solid #e7a11f;
}

.temporaryLabel {
  color: #e7a11f;
  font-style: italic;
}

.visible-desktop {
  display: none;
}
@media (min-width: 1025px) {
  .visible-desktop {
    display: block;
  }
}
  
.visible-tablet {
    display: none;
}
@media (min-width: 768px) and (max-width: 1024px) {
  .visible-tablet {
    display: block;
  }
}

.visible-mobile {
    display: none;
}
@media (max-width: 767px) {
  .visible-mobile {
    display: block;
  }
}

.us-checkbox-container {
  display: grid;
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

/* Bordi del checkbox */
.us-checkbox-container.checkboxWithBorder {
  padding: 8px 16px;
  border: solid 1px #ccc; /* Cambia con una variabile se necessario */
}

/* Posizionamento a destra */
.us-checkbox-container.positionLeft {
  flex-direction: row-reverse;
  justify-content: space-between;
  display: flex;
}

.us-checkbox-checkmark:after,
.us-checkbox-checkmark-green:after {
  content: "";
  position: absolute;
  display: none;
}

input[type="checkbox"]:checked ~ .us-checkbox-check:after {
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  display: block;
}

input[type="checkbox"]:checked ~ .us-checkbox-checkmark-green {
  background-color: #8bc94d !important;
  border: none;
}

input[type="checkbox"]:checked ~ .us-checkbox-check {
  background-color: #0f3250;
}

input[type="checkbox"] ~ .us-checkbox-check {
  border-radius: 4px;
}
  
.round {
  border-radius: 32px !important;
}

.error-checkmark {
  border-color: var(--alert-color) !important;
}

.us-checkbox-checkmark,
.us-checkbox-checkmark-green {
    display: block;
    position: relative;
    height: 24px;
    width: 24px;
    border: 2px solid #0f3250;
    overflow: hidden;
    background-color: #fff;
    cursor: pointer;
}

.us-checkbox-checkmark-green {
  display: flex;
  justify-content: center;
  align-items: center;
}

.us-checkbox-checkmark-green:after {
  aspect-ratio: 1 / 2;
  top: unset;
  left: unset;
  width: 6px;
  height: unset;
}

.us-checkbox-label {
  margin: 0.4rem 1rem;
  display: flex;
  align-items: center;
}