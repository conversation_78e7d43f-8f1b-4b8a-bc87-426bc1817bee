import { getFormState, setFieldValue } from 'c/formState';
import { fireEvent } from 'c/pubsub';
import { utils } from 'c/utils';
import { LightningElement, api, track } from 'lwc';

export default class FieldCheckbox extends LightningElement {
  _field;

  @track isValid = true;

  @api decodedValue;
  @api disabled;
  @api parentLayout;
  @api debug;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;

    setFieldValue(
      value.reference,
      value.value,
      'checkbox',
      value.required || value?.customAttributes?.required
    );
  }

  get label() {
    return this.field?.control.label ? utils.decodeHTML(this.field.control.label) : '';
  }

  get format() {
    if (this.field.labelFormat) return utils.getClassFromFormat(this.field.labelFormat);
  }

  get readonly() {
    return this.field.readOnly === true;
  }

  get componentClass() {
    return `pxCheckbox ${this.debug ? 'debug' : ''}`.trim();
  }

  get isChecked() {
    return this.field.value === 'true';
  }

  isCheckboxValid() {
    const state = getFormState();

    const reference = this.field?.reference;
    const touched = state.touchedFields.has(reference);

    if (!touched) return;

    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    this.isValid = isRequired && isTouched && value === '' ? false : true;
  }

  get checkboxClass() {
    const classes = [
      'us-checkbox-checkmark-green',
      'us-checkbox-check',
      this.field?.customAttributes?.style === 'Rounded' ? 'round' : '',
      this.field?.customAttributes?.style === 'Card' ? '' : '',
      this.field?.customAttributes?.ComponentPurpose === 'CheckPackage' ? 'round' : '',
    ];
    return classes.filter(Boolean).join(' ');
  }

  get containerClass() {
    let classes = ['us-checkbox-container'];

    if (this.field?.customAttributes?.checkboxWithBorder === 'true') {
      classes.push('checkboxWithBorder');
    }

    if (this.field?.customAttributes?.labelPositionWeb === 'left') {
      classes.push('positionLeft');
    }
    return classes.join(' ');
  }

  handleInputChange(evt) {
    const customEvent = {
      ...evt,
      target: {
        ...evt.target,
        checked: !this.isChecked,
      },
    };

    fireEvent('handleFieldChanged', {
      evt: customEvent,
      field: this.field,
      parentLayout: this.parentLayout,
    });
  }
}
