<template>
  <div class={componentClass}>
    <p if:true={debug} class="temporaryLabel">FIELD PX AUTO COMPLETE:</p>

    <c-custom-text-styles content={label} text-css={labelFormat}></c-custom-text-styles>

    <div class={autoCompleteClasses}>
      <div class={listClasses}>
        <span if:true={visualizedImage} class="image-container" style={selectedImage}></span>
        <input type="text" value={visualizedValue} disabled={disabled} readonly={isReadonly} placeholder={placeholder}
          data-reference={field.reference} oninput={handleInputChange} onfocus={dropdownClick} />
      </div>
      <ul class={listOptionsClasses}>
        <template for:each={options} for:item="option">
          <li key={option.value} data-key={option.value} class={option.className} onclick={handleOptionSelect}>
            {option.label}
            <span if:true={option.tooltip} class="image-zone" style={option.tooltip}></span>
          </li>
        </template>
      </ul>
    </div>
    <template if:false={isValid}>
      <div class="error-message">
        <i class="icon-Attenzione-pieno bd-icona"></i>
        <span class="testo-non-valido">{errorMessage}</span>
      </div>
    </template>
  </div>
</template>