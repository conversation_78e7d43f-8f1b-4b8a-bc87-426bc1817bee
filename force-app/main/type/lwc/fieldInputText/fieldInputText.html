<template>
  <div class={componentClass}>
    <p if:true={debug} class="temporaryLabel">FIELD PX TEXT INPUT:</p>
    <p if:true={debug} class="temporaryLabel">format: {format}</p>
    <p if:true={debug} class="temporaryLabel">labelFormat: {labelFormatDebug}</p>

    <c-custom-text-styles content={label} text-css={labelFormat}></c-custom-text-styles>

    <input style={style} class={textInputClass} type={formatReadWriteType.type} pattern={formatReadWriteType.pattern}
      title={formatReadWriteType.errorMessage} disabled={isDisabled} readonly={readonly} placeholder={placeholder}
      name={value} value={decodedValue} data-reference={field.reference} aria-describedby={tooltip}
      oninput={handleInputChange} onblur={fieldTouched} />

    <template if:false={isValid}>
      <div class="error-message">
        <i class="icon-Attenzione-pieno bd-icona"></i>
        <span class="testo-non-valido">{formatReadWriteType.errorMessage}</span>
      </div>
    </template>
  </div>
</template>