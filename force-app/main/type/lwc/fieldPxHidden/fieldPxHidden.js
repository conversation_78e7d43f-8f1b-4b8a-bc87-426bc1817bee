import { utils } from "c/utils";
import { utilsPega } from "c/utilsPega";
import { LightningElement, api, track } from "lwc";

/// <reference path="../utilsPega/utilsPega.jsdoc.js" />

export default class FieldPxHidden extends LightningElement {
  _field;


  /** @type {PxHiddenTypes} */
  @track _customType;
  
  @api decodedValue;
  @api disabled;
  @api debug;
  @api groups;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    //console.log("[DEBUG] Setter field - customAttributes:", this._field.customAttributes);
    const backgroundValue = utils.getCustomAttributeFromFieldUnsensitive(this._field, "background");
    //console.log("[DEBUG] Setter field - background:", this._field.customAttributes?.background);
    this._customType = utilsPega.pxHidden.mapPxHiddenType(
      this._field.customAttributes?.customType
    );
    //console.log("[DEBUG] Setter field - _customType:", this._customType);
    this.handleFieldChange();
  }

  get format() {
    return this.field.control.format;
  }

  get isVisible() {
    return (!this.isAnalyticsHidden && !!this.field.customAttributes.customType);
  }

  ///////// CUSTOM FIELD TYPES
  get isAnalyticsHidden() {
    return (
      this._customType ===
      utilsPega.pxHidden.PX_HIDDEN_TYPES.analyticsPageFields
    );
  }

  get isHeader() {
    
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.header;
  }

  get isStepper() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.stepper;
  }

  get isMultiStepper() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.multiStepper;
  }

  get isCircularStepper() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.circularStepper;
  }
  
  get isToastCard() {
     return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.toastCard;
  }

  get isCardGaranzieHeader() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.cardGaranzieHeader;
  }

  get isCardProtezioneDettaglioGaranzia() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.cardProtezioneDettaglioGaranzia;
  }

  get isAssurancePackage() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.assurancePackage;
  }

  get isCardSezione() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.cardSezione;
  }
  
  get isStickyFooter() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.stickyFooter;
  }

  get isCarrello() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.carrello;
  }

  get isUnicoProtezione() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.unicoProtezione;
  }

  get isCarouselCard() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.carouselCard;
  }

  get isBoxPagamento() {
    return this.customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.boxPagamento;
  }

  get isCardTelematica() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.cardTelematica;
  }
  
  get isAgencyLocator() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.agencyLocator;
  }

  get isBoxIndirizzo() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.boxIndirizzo;
  }

  get isAddressAutocomplete() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.addressAutocomplete;
  }

  get isCarouselWeb() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.carouselWeb;
  }

  get isCardProtezione() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.cardProtezione;
  }
  
  get isSeparator() {
    return this._customType === utilsPega.pxHidden.PX_HIDDEN_TYPES.separator;
  }

  handleFieldChange() {
    //console.log("[DEBUG] handleFieldChange - isFondoPagina:", this.isFondoPagina);
  }

  get customTypeForDebugging() {
    return (
      this.field.customAttributes?.customType ??
      this._customType ??
      "MISSING TYPE"
    );
  }

}
