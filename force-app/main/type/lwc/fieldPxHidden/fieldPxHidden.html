<template>
  <!-- Debug Information -->
  <template if:true={debug}>
  </template>

  <!-- Analytics Hidden Component -->
  <template if:true={isAnalyticsHidden}> 
  </template>

  <!-- Header Component -->
  <template if:true={isHeader}>
    <c-header field={field} groups={groups}></c-header>
  </template> 

  <template if:true={isStepper}>
    <c-stepper field={field} groups={groups} decoded-value={decodedValue} disabled={disabled}></c-stepper>
  </template>

  <template if:true={isMultiStepper}>
    <c-multi-stepper field={field} groups={groups}></c-multi-stepper>
  </template>
  
  <template if:true={isCircularStepper}>
    <c-circular-stepper field={field} groups={groups}></c-circular-stepper>
  </template>

  <template if:true={isToastCard}>
    <c-toast-card field={field}></c-toast-card>
  </template>
  
  <template if:true={isCardGaranzieHeader}>
    <c-card-garanzie-header field={field} groups={groups}></c-card-garanzie-header>
  </template>
  
  <template if:true={isAssurancePackage}>
    <c-assurance-package field={field} groups={groups}></c-assurance-package>
  </template>

  <template if:true={isCardProtezioneDettaglioGaranzia}>
    <c-card-protezione-dettaglio-garanzia field={field} groups={groups}></c-card-protezione-dettaglio-garanzia>
  </template>

  <template if:true={isCardSezione}>
    <c-card-sezione field={field} groups={groups}></c-card-sezione>
  </template>

  <template if:true={isStickyFooter}>
    <c-sticky-footer field={field} groups={groups}></c-sticky-footer>
  </template>

  <template if:true={isCarrello}>
    <c-carrello field={field} groups={groups}></c-carrello>
  </template>

  <template if:true={isUnicoProtezione}>
    <c-unico-protezione field={field} groups={groups}></c-unico-protezione>
  </template>

  <template if:true={isCarouselCard}>
    <c-carousel-card field={field} groups={groups}></c-carousel-card>
  </template>

  <template if:true={isBoxPagamento}>
    <c-box-pagamento field={field} groups={groups}></c-box-pagamento>
  </template>

  <template if:true={isCardTelematica}>
    <c-card-telematica field={field} groups={groups}></c-card-telematica>
  </template>

  <template if:true={isCarouselWeb}>
    <c-carousel-web view={view} groups={groups} field={field}></c-carousel-web>
  </template>

  <template if:true={isCardProtezione}>
    <c-card-protezione field={field} groups={groups}></c-card-protezione>
  </template>
  
  <template if:true={isAgencyLocator}>
    <c-dx-agency-locator field={field} groups={groups}></c-dx-agency-locator>
  </template>

  <template if:true={isBoxIndirizzo}>
    <c-box-indirizzo field={field} groups={groups}></c-box-indirizzo>
  </template>

  <template if:true={isAddressAutocomplete}>
    <c-address-autocomplete field={field} groups={groups}></c-address-autocomplete>
  </template>

  <template if:true={isSeparator}>
    <c-separator field={field} groups={groups}></c-separator>
  </template>

  <!-- Fallback for Unsupported Types -->
  <template if:false={isHeader}> 
  </template>
</template>
